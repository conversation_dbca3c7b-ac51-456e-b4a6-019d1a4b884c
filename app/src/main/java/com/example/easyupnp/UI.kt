package com.example.easyupnp

import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.Delete
import androidx.compose.material3.BottomAppBar
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.easyupnp.data.Device
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.easyupnp.interfaze.IDownloadUrlCallback
import com.example.easyupnp.viewmodel.DeviceListViewModel
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.TextButton
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll

private const val TAG = "UIContent"

@Preview
@Composable
fun Content() {
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = { TopBar() },
        bottomBar = { BottomBar() }
    ) { innerPadding ->
        Box(modifier = Modifier.padding(innerPadding)) {
            DeviceList()
        }
    }
}

@Preview
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TopBar() {
    val viewModel: DeviceListViewModel = viewModel()
    TopAppBar(
        title = {
            Text(viewModel.identifier)
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = MaterialTheme.colorScheme.primary,
            titleContentColor = MaterialTheme.colorScheme.onPrimary,
            actionIconContentColor = MaterialTheme.colorScheme.onPrimary
        ),
        actions = {
            IconButton(onClick = {
                viewModel.clearAll()
            }) {
                Icon(imageVector = Icons.Outlined.Delete, contentDescription = "")
            }
        }
    )
}

@Preview
@Composable
fun DeviceList() {
    @Composable
    fun Item(device: Device, viewModel: DeviceListViewModel) {
        var isExpanded by remember { mutableStateOf(false) }
        // 添加状态变量来控制对话框显示
        var showContentDialog by remember { mutableStateOf(false) }
        // 选中的ServiceType
        var uiSelectedServiceType by remember { mutableStateOf<String?>(null) }

        // 存储下载的内容
        var downloadedContent by remember { mutableStateOf<String?>(null) }
        var downloadedUrl by remember { mutableStateOf<String?>(null) }

        Card(
            modifier = Modifier
                .padding(4.dp)
                .fillMaxWidth(),
            onClick = { isExpanded = !isExpanded }
        ) {
            Column {
                Text(device.deviceDescription.deviceInfo?.friendlyName.toString(), fontSize = 16.sp, modifier = Modifier.padding(2.dp), fontWeight = FontWeight.Bold)
                Text(device.deviceDescription.deviceInfo?.modelName.toString(), fontSize = 14.sp, modifier = Modifier.padding(2.dp))
                Text(device.deviceId, fontSize = 14.sp, modifier = Modifier.padding(2.dp))

                AnimatedVisibility(
                    visible = isExpanded,
                    enter = fadeIn() + expandVertically(expandFrom = Alignment.Top),
                    exit = fadeOut() + shrinkVertically(shrinkTowards = Alignment.Top)
                ) {
                    HighlightedClickableText(
                        text = formatXmlString(device.deviceDescription.descXml),
                        modifier = Modifier.padding(top = 8.dp),
                         highlightPatterns = listOf(
                            "<serviceType>[^<]*</serviceType>"
                        ),
                        highlightColor = Color.Red,
                        onClick = { serviceType ->
                            viewModel.downloadXML(device, serviceType, object : IDownloadUrlCallback {
                                override fun onDownloadUrl(url: String, content: String?) {
                                    Log.d(TAG, "downloadService#onDownloadUrl >>> $url\n$content")
                                    // 更新状态变量，触发UI更新
                                    uiSelectedServiceType = serviceType
                                    downloadedContent = content
                                    downloadedUrl = url
                                    showContentDialog = true
                                }
                            })
                        }
                    )
                }
                
                // 添加对话框组件
                if (showContentDialog) {
                    AlertDialog(
                        onDismissRequest = { showContentDialog = false },
                        title = { Text(uiSelectedServiceType.toString()) },
                        text = { 
                            Column {
                                Text("URL: $downloadedUrl", 
                                    fontSize = 12.sp, 
                                    fontWeight = FontWeight.Bold,
                                    modifier = Modifier.padding(bottom = 8.dp)
                                )
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .height(300.dp)
                                        .verticalScroll(rememberScrollState())
                                ) {
                                    Text(
                                        text = downloadedContent ?: "No content available",
                                        fontSize = 12.sp
                                    )
                                }
                            }
                        },
                        confirmButton = {
                            TextButton(onClick = { showContentDialog = false }) {
                                Text("Close")
                            }
                        }
                    )
                }
            }
        }
    }

    val viewModel: DeviceListViewModel = viewModel()
    val device by viewModel.devices.observeAsState(initial = setOf())
    LazyColumn {
        items(device.toList()) {
            Item(device = it, viewModel = viewModel)
        }
    }
}

@Preview
@Composable
fun BottomBar() {
    val viewModel: DeviceListViewModel = viewModel()
    BottomAppBar(modifier = Modifier.fillMaxWidth(), actions = {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            Button(
                modifier = Modifier
                    .weight(1f)
                    .height(48.dp)
                    .padding(4.dp),
                onClick = {
                    Log.d(TAG, "call EasyUpnp#init >>> ${viewModel.init()}")
                }) {
                Text("Init", fontSize = 14.sp)
            }
            Button(
                modifier = Modifier
                    .weight(1f)
                    .height(48.dp)
                    .padding(4.dp),
                onClick = {
                    Log.d(TAG, "call EasyUpnp#discovery >>> ${viewModel.discovery()}")
                }) {
                Text("Discovery", fontSize = 14.sp)
            }
            Button(
                modifier = Modifier
                    .weight(1f)
                    .height(48.dp)
                    .padding(4.dp),
                onClick = {
                    Log.d(TAG, "call EasyUpnp#release >>> ${viewModel.release()}")
                }
            ) {
                Text("Release", fontSize = 14.sp)
            }
        }
    })
}

/**
 * 格式化 XML 字符串（添加标准缩进和换行）
 * @param xmlString 输入的 XML 字符串（可包含占位符）
 * @return 格式化后的 XML 字符串
 */
fun formatXmlString(xmlString: String): String {
    // 2. 简单缩进（按行添加 2 个空格）
    val lines = xmlString.lines()
    val indentedLines = lines.mapIndexed { index, line ->
        if (line.trim().startsWith("</")) {
            // 闭合标签不额外缩进
            "  $line"
        } else if (line.trim().isEmpty()) {
            // 空行保持不变
            line
        } else {
            // 其他行缩进 2 空格
            "  $line"
        }
    }

    return indentedLines.joinToString("\n")
}

/**
 * 高亮并使特定文本可点击的组件
 * @param text 完整文本内容
 * @param modifier Modifier修饰符
 * @param highlightPatterns 需要高亮的文本模式列表
 * @param highlightColor 高亮颜色
 * @param onClick 点击高亮文本的回调，参数为匹配到的文本
 */
@Composable
fun HighlightedClickableText(
    text: String,
    modifier: Modifier = Modifier,
    highlightPatterns: List<String>,
    highlightColor: Color = MaterialTheme.colorScheme.primary,
    onClick: (String) -> Unit
) {
    val annotatedString = buildAnnotatedString {
        append(text)

        // 为每个模式查找匹配并添加注解
        highlightPatterns.forEach { pattern ->
            val regex = pattern.toRegex(RegexOption.IGNORE_CASE)
            val matches = regex.findAll(text)

            matches.forEach { matchResult ->
                val start = matchResult.range.first
                val end = matchResult.range.last + 1
                val matchedText = matchResult.value
                
                // 提取标签内的内容
                val contentRegex = "<serviceType>([^<]*)</serviceType>".toRegex()
                val contentMatch = contentRegex.find(matchedText)
                val innerContent = contentMatch?.groupValues?.get(1) ?: matchedText

                // 添加点击注解，但存储的是内部内容
                addStringAnnotation(
                    tag = "CLICKABLE",
                    annotation = innerContent,
                    start = start,
                    end = end
                )

                // 添加颜色样式
                addStyle(
                    style = SpanStyle(
                        color = highlightColor,
                        fontWeight = FontWeight.Bold
                    ),
                    start = start,
                    end = end
                )
            }
        }
    }

    // 使用ClickableText显示带注解的文本
    androidx.compose.foundation.text.ClickableText(
        text = annotatedString,
        modifier = modifier,
        style = TextStyle(
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.onSurface
        ),
        onClick = { offset ->
            // 获取点击位置的注解
            annotatedString.getStringAnnotations(
                tag = "CLICKABLE",
                start = offset,
                end = offset
            ).firstOrNull()?.let { annotation ->
                // 调用回调函数，传递标签内的内容
                onClick(annotation.item)
            }
        }
    )
}
