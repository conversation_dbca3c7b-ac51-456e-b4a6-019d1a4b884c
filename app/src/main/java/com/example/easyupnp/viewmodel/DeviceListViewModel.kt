package com.example.easyupnp.viewmodel

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import com.example.easyupnp.EasyUpnp
import com.example.easyupnp.data.Device
import com.example.easyupnp.interfaze.IDownloadUrlCallback

class DeviceListViewModel : ViewModel() {

    private val easyUpnp by lazy { EasyUpnp() }

    val devices: LiveData<Set<Device>>
        get() = easyUpnp.devices

    val identifier: String
        get() = easyUpnp.identifier()

    fun init(hostIp: String? = null, descPort: Int = 0) = easyUpnp.init(hostIp, descPort)

    fun discovery() {
        val target = "urn:schemas-upnp-org:device:MediaRenderer:1"
        easyUpnp.discovery(SEARCH_MAX_SECONDS, target)
    }

    fun clearAll() = easyUpnp.clearAll()

    fun release() = easyUpnp.release()

    fun downloadXML(device: Device, callback: IDownloadUrlCallback) {
        val baseUrl = device.deviceDescription.urlBase
        if (baseUrl.isNullOrBlank()) {
            return
        }

        val scpdUrl = device.deviceDescription.deviceInfo?.serviceList?.firstOrNull { service ->
            "urn:upnp-org:serviceId:AVTransport" == service.serviceId
        }?.SCPDURL
        if (scpdUrl.isNullOrBlank()) {
            return
        }

        val fullUrl = baseUrl + scpdUrl
        Log.d(TAG, "downloadAVTransport >>> $fullUrl")
        easyUpnp.download(fullUrl, callback)
    }

    companion object {
        private const val SEARCH_MAX_SECONDS = 10L

        private const val TAG = "DeviceListViewModel"
    }
}